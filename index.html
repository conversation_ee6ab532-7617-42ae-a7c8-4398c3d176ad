<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let interactionCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            let lastInteractionTime = 0;
            let isIframeActive = false;

            // Track when iframe becomes active/inactive
            iframe.addEventListener('mouseenter', () => {
                isIframeActive = true;
            });

            iframe.addEventListener('mouseleave', () => {
                isIframeActive = false;
            });

            // Method 1: Focus detection - when iframe gets focus (user clicked)
            iframe.addEventListener('focus', () => {
                if (isIframeActive) {
                    registerInteraction('focus');
                }
            });

            // Method 2: Window blur detection - when focus moves to iframe
            let windowWasFocused = true;
            window.addEventListener('focus', () => {
                windowWasFocused = true;
            });

            window.addEventListener('blur', () => {
                if (windowWasFocused && isIframeActive) {
                    setTimeout(() => {
                        if (document.activeElement === iframe) {
                            registerInteraction('blur');
                        }
                    }, 50);
                }
                windowWasFocused = false;
            });

            // Method 3: Mouse down detection on iframe container
            const container = document.getElementById('iframe-container');
            container.addEventListener('mousedown', (e) => {
                if (e.target === iframe || iframe.contains(e.target)) {
                    registerInteraction('mousedown');
                }
            });

            function registerInteraction(source) {
                const now = Date.now();

                // Debounce: prevent multiple detections within 800ms
                if (now - lastInteractionTime < 800) {
                    console.log(`Interaction ignored: ${source} (debounced)`);
                    return;
                }

                lastInteractionTime = now;
                interactionCount++;
                counter.textContent = interactionCount;

                // Show feedback in center of iframe
                const iframeRect = iframe.getBoundingClientRect();
                feedback.style.left = (iframeRect.left + iframeRect.width / 2 - 50) + 'px';
                feedback.style.top = (iframeRect.top + iframeRect.height / 2 - 15) + 'px';
                feedback.textContent = `Click #${interactionCount}`;
                feedback.style.display = 'block';

                setTimeout(() => {
                    feedback.style.display = 'none';
                }, 1000);

                console.log(`✓ Interaction #${interactionCount} detected via ${source}`);
            }
        })();
    </script>
</body>
</html>
 