<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let clickCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            let lastClickTime = 0;
            let mouseIsOverIframe = false;

            // Track when mouse is over iframe
            iframe.addEventListener('mouseenter', () => {
                mouseIsOverIframe = true;
            });

            iframe.addEventListener('mouseleave', () => {
                mouseIsOverIframe = false;
            });

            // ONLY method: Window blur detection for iframe clicks
            // This is the most reliable way to detect clicks inside iframe content
            window.addEventListener('blur', () => {
                // Small delay to let iframe gain focus
                setTimeout(() => {
                    // Check if:
                    // 1. Mouse was over iframe when window lost focus
                    // 2. Iframe now has focus (meaning user clicked inside it)
                    // 3. Enough time has passed since last click (debounce)
                    const now = Date.now();

                    if (mouseIsOverIframe &&
                        document.activeElement === iframe &&
                        (now - lastClickTime > 500)) {

                        // This is a genuine click inside iframe content
                        lastClickTime = now;
                        clickCount++;
                        counter.textContent = clickCount;

                        // Show feedback
                        const iframeRect = iframe.getBoundingClientRect();
                        feedback.style.left = (iframeRect.left + iframeRect.width / 2 - 50) + 'px';
                        feedback.style.top = (iframeRect.top + 30) + 'px';
                        feedback.textContent = `Click #${clickCount}`;
                        feedback.style.display = 'block';
                        feedback.style.backgroundColor = 'rgba(52, 152, 219, 0.9)';
                        feedback.style.color = 'white';
                        feedback.style.padding = '6px 12px';
                        feedback.style.borderRadius = '4px';
                        feedback.style.fontSize = '14px';
                        feedback.style.fontWeight = 'bold';

                        setTimeout(() => {
                            feedback.style.display = 'none';
                        }, 1500);

                        console.log(`✅ Click #${clickCount} detected inside iframe`);
                    }
                }, 50);
            });

            console.log('Simple iframe click tracker ready');
        })();
    </script>
</body>
</html>
 