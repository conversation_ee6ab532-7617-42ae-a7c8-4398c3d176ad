<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }
        #click-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 5;
            background-color: transparent;
            cursor: pointer;
        }
        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-overlay"></div>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let interactionCount = 0;
            const overlay = document.getElementById('click-overlay');
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            let isOverIframe = false;
            
            // Track mouse position over iframe
            iframe.addEventListener('mouseover', () => isOverIframe = true);
            iframe.addEventListener('mouseout', () => isOverIframe = false);
            
            // Method 1: Overlay click (works for first click)
            overlay.addEventListener('click', function(e) {
                registerInteraction(e);
                
                // Temporarily disable overlay
                this.style.pointerEvents = 'none';
                setTimeout(() => this.style.pointerEvents = 'auto', 100);
            });
            
            // Method 2: Blur detection (works for subsequent clicks)
            window.addEventListener('blur', () => {
                setTimeout(() => {
                    if (document.activeElement === iframe && isOverIframe) {
                        registerInteraction();
                    }
                }, 10);
            });
            
            // Method 3: URL change detection (for navigation)
            let lastUrl = iframe.src;
            setInterval(() => {
                try {
                    if (iframe.contentWindow.location.href !== lastUrl) {
                        lastUrl = iframe.contentWindow.location.href;
                        registerInteraction();
                    }
                } catch (e) {
                    // Cross-origin error expected
                }
            }, 300);
            
            function registerInteraction(e) {
                // Debounce rapid successive events
                const now = Date.now();
                if (now - (window.lastInteractionTime || 0) < 200) return;
                window.lastInteractionTime = now;
                
                interactionCount++;
                counter.textContent = interactionCount;
                
                if (e) {
                    feedback.style.left = (e.clientX - 50) + 'px';
                    feedback.style.top = (e.clientY - 30) + 'px';
                }
                feedback.textContent = `Interaction #${interactionCount}`;
                feedback.style.display = 'block';
                setTimeout(() => feedback.style.display = 'none', 800);
                
                console.log('Interaction detected:', interactionCount);
            }
        })();
    </script>
</body>
</html>
Cricket"></iframe>
 