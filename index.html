<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let interactionCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            let lastInteractionTime = 0;
            let mouseOverIframe = false;
            let iframeHasFocus = false;

            // Track mouse position over iframe
            iframe.addEventListener('mouseenter', () => {
                mouseOverIframe = true;
                console.log('Mouse entered iframe');
            });

            iframe.addEventListener('mouseleave', () => {
                mouseOverIframe = false;
                console.log('Mouse left iframe');
            });

            // Track iframe focus state
            iframe.addEventListener('focus', () => {
                iframeHasFocus = true;
                console.log('Iframe gained focus');
            });

            iframe.addEventListener('blur', () => {
                iframeHasFocus = false;
                console.log('Iframe lost focus');
            });

            // Method 1: Window blur detection (most reliable for iframe clicks)
            window.addEventListener('blur', () => {
                // Small delay to ensure iframe gets focus
                setTimeout(() => {
                    if (mouseOverIframe && document.activeElement === iframe) {
                        registerInteraction('window-blur');
                    }
                }, 10);
            });

            // Method 2: Document click detection with iframe check
            document.addEventListener('click', (e) => {
                // If click is on iframe or very close to it, and mouse is over iframe
                const iframeRect = iframe.getBoundingClientRect();
                const clickX = e.clientX;
                const clickY = e.clientY;

                if (clickX >= iframeRect.left && clickX <= iframeRect.right &&
                    clickY >= iframeRect.top && clickY <= iframeRect.bottom) {
                    registerInteraction('document-click');
                }
            });

            // Method 3: Iframe load detection (for navigation within iframe)
            let lastUrl = iframe.src;
            let urlCheckInterval = setInterval(() => {
                try {
                    // This will throw an error for cross-origin, but we can detect URL changes
                    const currentUrl = iframe.contentWindow.location.href;
                    if (currentUrl !== lastUrl && lastUrl !== iframe.src) {
                        lastUrl = currentUrl;
                        registerInteraction('navigation');
                    }
                } catch (e) {
                    // Cross-origin restriction - expected
                    // We can still detect some navigation by checking if iframe reloads
                }
            }, 500);

            // Method 4: Message passing (if iframe supports it)
            window.addEventListener('message', (e) => {
                if (e.source === iframe.contentWindow && e.data.type === 'click') {
                    registerInteraction('message');
                }
            });

            function registerInteraction(source) {
                const now = Date.now();

                // Debounce: prevent multiple detections within 1 second
                if (now - lastInteractionTime < 1000) {
                    console.log(`❌ Interaction ignored: ${source} (debounced - ${now - lastInteractionTime}ms ago)`);
                    return;
                }

                lastInteractionTime = now;
                interactionCount++;
                counter.textContent = interactionCount;

                // Show feedback
                const iframeRect = iframe.getBoundingClientRect();
                feedback.style.left = (iframeRect.left + iframeRect.width / 2 - 50) + 'px';
                feedback.style.top = (iframeRect.top + 50) + 'px';
                feedback.textContent = `Click #${interactionCount} (${source})`;
                feedback.style.display = 'block';
                feedback.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';

                setTimeout(() => {
                    feedback.style.display = 'none';
                }, 1500);

                console.log(`✅ Click #${interactionCount} detected via ${source} at ${new Date().toLocaleTimeString()}`);
            }

            // Clean up interval on page unload
            window.addEventListener('beforeunload', () => {
                clearInterval(urlCheckInterval);
            });
        })();
    </script>
</body>
</html>
 