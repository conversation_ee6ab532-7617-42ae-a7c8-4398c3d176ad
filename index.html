<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
        #click-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
            background-color: transparent;
            pointer-events: auto;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-overlay"></div>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let clickCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            const overlay = document.getElementById('click-overlay');
            let lastClickTime = 0;

            // Simple and reliable: Overlay click detection with immediate pass-through
            overlay.addEventListener('click', function(e) {
                const now = Date.now();

                // Debounce to prevent double counting
                if (now - lastClickTime < 200) {
                    return;
                }

                lastClickTime = now;
                clickCount++;
                counter.textContent = clickCount;

                // Show feedback at click position
                feedback.style.left = (e.clientX - 50) + 'px';
                feedback.style.top = (e.clientY - 30) + 'px';
                feedback.textContent = `Click #${clickCount}`;
                feedback.style.display = 'block';
                feedback.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';
                feedback.style.color = 'white';
                feedback.style.padding = '6px 12px';
                feedback.style.borderRadius = '4px';
                feedback.style.fontSize = '14px';
                feedback.style.fontWeight = 'bold';
                feedback.style.zIndex = '1000';

                setTimeout(() => {
                    feedback.style.display = 'none';
                }, 1500);

                console.log(`✅ Click #${clickCount} detected at (${e.clientX}, ${e.clientY})`);

                // Immediately pass the click through to the iframe
                // Temporarily disable overlay
                overlay.style.pointerEvents = 'none';

                // Get the element under the click position
                const elementBelow = document.elementFromPoint(e.clientX, e.clientY);

                // Re-enable overlay after a very short delay
                setTimeout(() => {
                    overlay.style.pointerEvents = 'auto';
                }, 50);

                // Simulate click on the iframe at the same position
                setTimeout(() => {
                    const rect = iframe.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    // Create and dispatch a click event to the iframe
                    const clickEvent = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: e.clientX,
                        clientY: e.clientY,
                        button: 0
                    });

                    // Try to click on the iframe
                    iframe.dispatchEvent(clickEvent);

                    // Also try to focus the iframe to ensure it receives the click
                    iframe.focus();

                    // If we can access the iframe content, try to click there too
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const iframeElement = iframeDoc.elementFromPoint(x, y);
                        if (iframeElement) {
                            iframeElement.click();
                        }
                    } catch (e) {
                        // Cross-origin restriction - expected for external sites
                        console.log('Cross-origin click simulation attempted');
                    }
                }, 10);
            });

            console.log('🚀 Simple overlay click tracker initialized');
        })();
    </script>
</body>
</html>
 