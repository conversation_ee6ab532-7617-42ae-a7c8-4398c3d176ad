<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let interactionCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            let lastInteractionTime = 0;
            let mouseOverIframe = false;
            let lastMouseActivity = 0;
            let isWindowFocused = true;

            // Track mouse position over iframe
            iframe.addEventListener('mouseenter', () => {
                mouseOverIframe = true;
                console.log('🖱️ Mouse entered iframe');
            });

            iframe.addEventListener('mouseleave', () => {
                mouseOverIframe = false;
                console.log('🖱️ Mouse left iframe');
            });

            // Track mouse movement over iframe to detect activity
            iframe.addEventListener('mousemove', () => {
                lastMouseActivity = Date.now();
            });

            // Track window focus state
            window.addEventListener('focus', () => {
                isWindowFocused = true;
                console.log('🔍 Window gained focus');
            });

            window.addEventListener('blur', () => {
                const now = Date.now();
                console.log('🔍 Window lost focus');

                // If mouse was over iframe recently and window loses focus, it's likely a click
                if (mouseOverIframe && (now - lastMouseActivity < 2000)) {
                    setTimeout(() => {
                        if (document.activeElement === iframe) {
                            registerInteraction('window-blur');
                        }
                    }, 50);
                }
                isWindowFocused = false;
            });

            // Method 1: Mouse down detection on iframe
            iframe.addEventListener('mousedown', (e) => {
                console.log('🖱️ Mouse down on iframe');
                registerInteraction('mousedown');
            });

            // Method 2: Focus detection with mouse activity check
            iframe.addEventListener('focus', () => {
                const now = Date.now();
                console.log('🔍 Iframe gained focus');

                // If focus happened recently after mouse activity, it's likely a click
                if (now - lastMouseActivity < 1000) {
                    registerInteraction('focus-after-mouse');
                }
            });

            // Method 3: Periodic activity detection
            let activityCheckInterval = setInterval(() => {
                // Check if iframe has focus and there was recent mouse activity
                if (document.activeElement === iframe && mouseOverIframe) {
                    const now = Date.now();

                    // If there was mouse activity in the last 500ms and we haven't counted recently
                    if ((now - lastMouseActivity < 500) && (now - lastInteractionTime > 1000)) {
                        registerInteraction('activity-detection');
                    }
                }
            }, 200);

            // Method 4: Visibility change detection (for navigation)
            document.addEventListener('visibilitychange', () => {
                if (document.hidden && mouseOverIframe) {
                    setTimeout(() => {
                        if (document.activeElement === iframe) {
                            registerInteraction('visibility-change');
                        }
                    }, 100);
                }
            });

            // Method 5: Click detection on iframe container
            const container = document.getElementById('iframe-container');
            container.addEventListener('click', (e) => {
                const rect = iframe.getBoundingClientRect();
                if (e.clientX >= rect.left && e.clientX <= rect.right &&
                    e.clientY >= rect.top && e.clientY <= rect.bottom) {
                    registerInteraction('container-click');
                }
            });

            function registerInteraction(source) {
                const now = Date.now();

                // Debounce: prevent multiple detections within 800ms
                if (now - lastInteractionTime < 800) {
                    console.log(`❌ Interaction ignored: ${source} (debounced - ${now - lastInteractionTime}ms ago)`);
                    return;
                }

                lastInteractionTime = now;
                interactionCount++;
                counter.textContent = interactionCount;

                // Show feedback
                const iframeRect = iframe.getBoundingClientRect();
                feedback.style.left = (iframeRect.left + iframeRect.width / 2 - 75) + 'px';
                feedback.style.top = (iframeRect.top + 20) + 'px';
                feedback.textContent = `✅ Click #${interactionCount}`;
                feedback.style.display = 'block';
                feedback.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';
                feedback.style.color = 'white';
                feedback.style.padding = '8px 12px';
                feedback.style.borderRadius = '4px';
                feedback.style.fontSize = '14px';
                feedback.style.fontWeight = 'bold';

                setTimeout(() => {
                    feedback.style.display = 'none';
                }, 2000);

                console.log(`✅ CLICK #${interactionCount} detected via ${source} at ${new Date().toLocaleTimeString()}`);
            }

            // Clean up intervals on page unload
            window.addEventListener('beforeunload', () => {
                clearInterval(activityCheckInterval);
            });

            console.log('🚀 Iframe click tracker initialized');
        })();
    </script>
</body>
</html>
 