<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
        #click-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            background-color: transparent;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-overlay"></div>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let clickCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            let lastClickTime = 0;
            let mouseOverIframe = false;
            let windowHasFocus = true;

            // Track mouse over iframe
            iframe.addEventListener('mouseenter', () => {
                mouseOverIframe = true;
                console.log('Mouse over iframe');
            });

            iframe.addEventListener('mouseleave', () => {
                mouseOverIframe = false;
                console.log('Mouse left iframe');
            });

            // Track window focus
            window.addEventListener('focus', () => {
                windowHasFocus = true;
            });

            // Main detection: Window blur when mouse is over iframe
            window.addEventListener('blur', () => {
                console.log('Window blur detected, mouse over iframe:', mouseOverIframe);

                if (mouseOverIframe) {
                    setTimeout(() => {
                        // Check if iframe now has focus (indicating a click inside)
                        if (document.activeElement === iframe) {
                            const now = Date.now();

                            // Debounce
                            if (now - lastClickTime > 400) {
                                lastClickTime = now;
                                clickCount++;
                                counter.textContent = clickCount;

                                // Show feedback
                                const rect = iframe.getBoundingClientRect();
                                feedback.style.left = (rect.left + rect.width / 2 - 50) + 'px';
                                feedback.style.top = (rect.top + 20) + 'px';
                                feedback.textContent = `Click #${clickCount}`;
                                feedback.style.display = 'block';
                                feedback.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';
                                feedback.style.color = 'white';
                                feedback.style.padding = '6px 12px';
                                feedback.style.borderRadius = '4px';
                                feedback.style.fontSize = '14px';
                                feedback.style.fontWeight = 'bold';

                                setTimeout(() => {
                                    feedback.style.display = 'none';
                                }, 1500);

                                console.log(`✅ Click #${clickCount} detected inside iframe`);
                            }
                        }
                    }, 100);
                }
                windowHasFocus = false;
            });

            // Alternative method: Container click detection
            const container = document.getElementById('iframe-container');
            container.addEventListener('mousedown', (e) => {
                // Check if click is within iframe bounds
                const rect = iframe.getBoundingClientRect();
                if (e.clientX >= rect.left && e.clientX <= rect.right &&
                    e.clientY >= rect.top && e.clientY <= rect.bottom) {

                    const now = Date.now();
                    if (now - lastClickTime > 400) {
                        lastClickTime = now;
                        clickCount++;
                        counter.textContent = clickCount;

                        // Show feedback
                        feedback.style.left = (e.clientX - 50) + 'px';
                        feedback.style.top = (e.clientY - 30) + 'px';
                        feedback.textContent = `Click #${clickCount}`;
                        feedback.style.display = 'block';
                        feedback.style.backgroundColor = 'rgba(52, 152, 219, 0.9)';
                        feedback.style.color = 'white';
                        feedback.style.padding = '6px 12px';
                        feedback.style.borderRadius = '4px';
                        feedback.style.fontSize = '14px';
                        feedback.style.fontWeight = 'bold';

                        setTimeout(() => {
                            feedback.style.display = 'none';
                        }, 1500);

                        console.log(`✅ Click #${clickCount} detected via container`);
                    }
                }
            });

            console.log('Iframe click tracker initialized');
        })();
    </script>
</body>
</html>
 