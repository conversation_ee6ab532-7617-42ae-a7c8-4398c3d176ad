<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Perfect Iframe Click Tracker</title>
    <style>
        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }
        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        .click-feedback {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
            z-index: 10;
        }
        #click-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            background-color: transparent;
            pointer-events: auto;
        }
    </style>
</head>
<body>
    <h2>Iframe Interaction Tracker</h2>
    <p>Total Interactions: <span id="click-counter">0</span></p>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-overlay"></div>
        <div id="click-feedback" class="click-feedback"></div>
    </div>
 
    <script>
        (function() {
            let clickCount = 0;
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            const overlay = document.getElementById('click-overlay');
            let lastClickTime = 0;

            // Method: Overlay click detection with pass-through
            overlay.addEventListener('mousedown', function(e) {
                const now = Date.now();

                // Debounce clicks
                if (now - lastClickTime < 300) {
                    return;
                }

                lastClickTime = now;
                clickCount++;
                counter.textContent = clickCount;

                // Show feedback
                feedback.style.left = (e.clientX - 50) + 'px';
                feedback.style.top = (e.clientY - 30) + 'px';
                feedback.textContent = `Click #${clickCount}`;
                feedback.style.display = 'block';
                feedback.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';
                feedback.style.color = 'white';
                feedback.style.padding = '6px 12px';
                feedback.style.borderRadius = '4px';
                feedback.style.fontSize = '14px';
                feedback.style.fontWeight = 'bold';

                setTimeout(() => {
                    feedback.style.display = 'none';
                }, 1500);

                console.log(`✅ Click #${clickCount} detected`);

                // Temporarily disable overlay to let click pass through
                overlay.style.pointerEvents = 'none';

                // Re-enable overlay after a short delay
                setTimeout(() => {
                    overlay.style.pointerEvents = 'auto';
                }, 100);

                // Simulate the click on the iframe
                const rect = iframe.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Create a synthetic click event for the iframe
                setTimeout(() => {
                    const clickEvent = new MouseEvent('click', {
                        clientX: e.clientX,
                        clientY: e.clientY,
                        bubbles: true,
                        cancelable: true
                    });
                    iframe.dispatchEvent(clickEvent);
                }, 10);
            });

            console.log('Overlay click tracker ready');
        })();
    </script>
</body>
</html>
 